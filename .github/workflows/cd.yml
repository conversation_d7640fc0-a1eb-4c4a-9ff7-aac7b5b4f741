name: 🚀 CD Pipeline

on:
  workflow_run:
    workflows: ["🔄 CI Pipeline"]
    types: [completed]
    branches: [main]

jobs:
  deploy:
    name: 🚀 Deploy
    runs-on: ubuntu-latest
    if: github.event.workflow_run.conclusion == 'success'

    steps:
      - name: 📥 Checkout
        uses: actions/checkout@v4

      - name: � Setup Docker Buildx
        uses: docker/setup-buildx-action@v3

      - name: 🔐 Login to Docker Hub
        uses: docker/login-action@v3
        with:
          username: ${{ secrets.DOCKERHUB_USERNAME }}
          password: ${{ secrets.DOCKERHUB_TOKEN }}

      - name: 🏗️ Build and Push Docker Images
        run: |
          # Build and tag images using your existing Dockerfiles
          docker build -t ${{ secrets.DOCKERHUB_USERNAME }}/ukshati-frontend:${{ github.sha }} .
          docker build -f Dockerfile.mysql -t ${{ secrets.DOCKERHUB_USERNAME }}/ukshati-database:${{ github.sha }} .
          # Push images to Docker Hub
          docker push ${{ secrets.DOCKERHUB_USERNAME }}/ukshati-frontend:${{ github.sha }}
          docker push ${{ secrets.DOCKERHUB_USERNAME }}/ukshati-database:${{ github.sha }}

      - name: 🚀 Simulate Deployment
        run: |
          echo "🧪 Simulating deployment..."
          echo "✅ Docker images would be deployed to a server here"
          echo "✅ Deployment simulation completed"

      - name: 📢 Notify Success
        if: success()
        run: echo "🎉 Successfully completed CD pipeline!"

      - name: 📢 Notify Failure
        if: failure()
        run: echo "❌ CD pipeline failed!"
