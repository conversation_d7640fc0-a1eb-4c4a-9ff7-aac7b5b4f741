
name: 🔄 CI Pipeline

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main ]
  workflow_dispatch:

concurrency:
  group: ${{ github.workflow }}-${{ github.ref }}
  cancel-in-progress: true

permissions:
  contents: read
  packages: write

env:
  NODE_VERSION: '20'
  MYSQL_ROOT_PASSWORD: root
  MYSQL_DATABASE: company_db
  MYSQL_USER: company
  MYSQL_PASSWORD: Ukshati@123

jobs:
  # 1. Code Quality & Linting
  lint-and-format:
    name: 🔍 Code Quality
    runs-on: ubuntu-latest
    steps:
      - name: Checkout repository
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'
          cache-dependency-path: frontend/package-lock.json

      - name: Install dependencies
        working-directory: ./frontend
        run: npm ci

      - name: Run ESLint
        working-directory: ./frontend
        run: npm run lint

      - name: Check TypeScript (if applicable)
        working-directory: ./frontend
        run: npx tsc --noEmit || echo "No TypeScript files found"

  # 2. Unit & Component Tests
  test-frontend:
    name: 🧪 Frontend Tests
    runs-on: ubuntu-latest
    steps:
      - name: Checkout repository
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'
          cache-dependency-path: frontend/package-lock.json

      - name: Install dependencies
        working-directory: ./frontend
        run: npm ci

      - name: Test Component Imports
        working-directory: ./frontend
        run: npm run test:components

      - name: Test Dashboard Components
        working-directory: ./frontend
        run: npm run test:dashboard

      - name: Test All Pages Structure
        working-directory: ./frontend
        run: npm run test:pages

  # 3. API & Integration Tests
  test-api:
    name: 🔌 API Tests
    runs-on: ubuntu-latest
    services:
      mysql:
        image: mysql:8.0
        env:
          MYSQL_ROOT_PASSWORD: ${{ env.MYSQL_ROOT_PASSWORD }}
          MYSQL_DATABASE: ${{ env.MYSQL_DATABASE }}
          MYSQL_USER: ${{ env.MYSQL_USER }}
          MYSQL_PASSWORD: ${{ env.MYSQL_PASSWORD }}
        ports:
          - 3306:3306
        options: >-
          --health-cmd="mysqladmin ping -h localhost"
          --health-interval=10s
          --health-timeout=5s
          --health-retries=5

    steps:
      - name: Checkout repository
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'
          cache-dependency-path: frontend/package-lock.json

      - name: Install dependencies
        working-directory: ./frontend
        run: npm ci

      - name: Wait for MySQL
        run: |
          until mysqladmin ping -h 127.0.0.1 -u root -p${{ env.MYSQL_ROOT_PASSWORD }} --silent; do
            echo 'Waiting for MySQL...'
            sleep 2
          done

      - name: Initialize Database
        run: |
          mysql -h 127.0.0.1 -u root -p${{ env.MYSQL_ROOT_PASSWORD }} < db/company_db.sql

      - name: Start Application
        working-directory: ./frontend
        run: |
          npm run build
          npm start &
          sleep 10
        env:
          DB_HOST: 127.0.0.1
          DB_USER: ${{ env.MYSQL_USER }}
          DB_PASSWORD: ${{ env.MYSQL_PASSWORD }}
          DB_NAME: ${{ env.MYSQL_DATABASE }}

      - name: Run API Tests
        working-directory: ./frontend
        run: npm run test:api
        env:
          API_BASE: http://localhost:3000

      - name: Run Performance Tests
        working-directory: ./frontend
        run: npm run test:performance
        env:
          API_BASE: http://localhost:3000

  # 4. Docker Build & Test
  test-docker:
    name: 🐳 Docker Build
    runs-on: ubuntu-latest
    steps:
      - name: Checkout repository
        uses: actions/checkout@v4

      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v3

      - name: Build Docker Images
        run: |
          docker-compose build --no-cache

      - name: Start Services
        run: |
          docker-compose up -d
          sleep 30

      - name: Check Service Health
        run: |
          docker-compose ps
          docker-compose logs nextjs
          docker-compose logs db

      - name: Test Docker API Endpoints
        run: |
          docker-compose exec -T nextjs npm run test:docker

      - name: Cleanup
        run: docker-compose down -v

  # 5. Build & Security Scan
  build-and-scan:
    name: 🔒 Build & Security
    runs-on: ubuntu-latest
    needs: [lint-and-format, test-frontend]
    steps:
      - name: Checkout repository
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'
          cache-dependency-path: frontend/package-lock.json

      - name: Install dependencies
        working-directory: ./frontend
        run: npm ci

      - name: Build Application
        working-directory: ./frontend
        run: npm run build

      - name: Run Security Audit
        working-directory: ./frontend
        run: npm audit --audit-level=high || echo "Security audit completed with warnings"

      - name: Upload Build Artifacts
        uses: actions/upload-artifact@v4
        with:
          name: build-files
          path: frontend/.next/
          retention-days: 7